# Generated by Django 5.2.4 on 2025-07-05 17:30

import django.contrib.postgres.search
import django.core.validators
import django.db.models.constraints
import django.db.models.deletion
import venues_app.models
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category_name', models.CharField(help_text='Category name (e.g., Spa, Massage, Salon)', max_length=100, verbose_name='category name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=120, null=True, unique=True, verbose_name='slug')),
                ('category_description', models.TextField(blank=True, help_text='Optional description of the category', verbose_name='description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active and visible', verbose_name='active status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'ordering': ['category_name'],
                'indexes': [models.Index(fields=['category_name'], name='venues_app__categor_7e4253_idx'), models.Index(fields=['is_active'], name='venues_app__is_acti_72ab66_idx')],
            },
        ),
        migrations.CreateModel(
            name='ServiceCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Service category name (e.g., Massage, Facial, Hair)', max_length=100, unique=True, verbose_name='category name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=120, null=True, unique=True, verbose_name='slug')),
                ('description', models.TextField(blank=True, help_text='Optional description of the service category', verbose_name='description')),
                ('icon_class', models.CharField(blank=True, help_text='CSS icon class for display (e.g., fas fa-spa)', max_length=50, verbose_name='icon class')),
                ('color_code', models.CharField(blank=True, help_text='Hex color code for category display (e.g., #FF5722)', max_length=7, verbose_name='color code')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is active and visible', verbose_name='active status')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Display order for category listings', verbose_name='sort order')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'Service Category',
                'verbose_name_plural': 'Service Categories',
                'ordering': ['sort_order', 'name'],
                'indexes': [models.Index(fields=['is_active'], name='venues_app__is_acti_0cbaef_idx'), models.Index(fields=['sort_order'], name='venues_app__sort_or_b662c2_idx')],
            },
        ),
        migrations.CreateModel(
            name='ServiceTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Tag name (e.g., relaxing, deep-tissue, pain-relief)', max_length=50, unique=True, verbose_name='tag name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=60, null=True, unique=True, verbose_name='slug')),
                ('tag_type', models.CharField(choices=[('keyword', 'Keyword'), ('technique', 'Technique'), ('benefit', 'Benefit'), ('body_part', 'Body Part'), ('duration', 'Duration'), ('intensity', 'Intensity')], default='keyword', help_text='Type of tag for better organization', max_length=20, verbose_name='tag type')),
                ('description', models.TextField(blank=True, help_text='Optional description of what this tag represents', max_length=200, verbose_name='description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this tag is available for use', verbose_name='active status')),
                ('usage_count', models.PositiveIntegerField(default=0, help_text='Number of times this tag has been used (auto-updated)', verbose_name='usage count')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'Service Tag',
                'verbose_name_plural': 'Service Tags',
                'ordering': ['tag_type', 'name'],
                'indexes': [models.Index(fields=['is_active'], name='venues_app__is_acti_a997f4_idx'), models.Index(fields=['tag_type'], name='venues_app__tag_typ_23f395_idx'), models.Index(fields=['usage_count'], name='venues_app__usage_c_5b3832_idx')],
            },
        ),
        migrations.CreateModel(
            name='USCity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city', models.CharField(db_index=True, help_text='City name', max_length=100, verbose_name='city')),
                ('state_id', models.CharField(db_index=True, help_text='State abbreviation (e.g., NY, CA)', max_length=2, verbose_name='state abbreviation')),
                ('state_name', models.CharField(db_index=True, help_text='Full state name', max_length=100, verbose_name='state name')),
                ('county_name', models.CharField(db_index=True, help_text='County name', max_length=100, verbose_name='county name')),
                ('latitude', models.DecimalField(blank=True, decimal_places=7, help_text='Latitude coordinate', max_digits=10, null=True, verbose_name='latitude')),
                ('longitude', models.DecimalField(blank=True, decimal_places=7, help_text='Longitude coordinate', max_digits=10, null=True, verbose_name='longitude')),
                ('zip_codes', models.TextField(blank=True, help_text='Space-separated list of ZIP codes', verbose_name='ZIP codes')),
                ('city_id', models.CharField(help_text='Unique identifier for the city', max_length=20, unique=True, verbose_name='city ID')),
                ('search_vector', models.TextField(blank=True, db_index=True, help_text='Precomputed search text for faster queries', verbose_name='search vector')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
            ],
            options={
                'verbose_name': 'US City',
                'verbose_name_plural': 'US Cities',
                'ordering': ['state_name', 'city'],
                'indexes': [models.Index(fields=['state_name', 'county_name'], name='uscity_state_county_idx'), models.Index(fields=['state_name', 'city'], name='uscity_state_city_idx'), models.Index(fields=['state_name', 'county_name', 'city'], name='uscity_full_location_idx'), models.Index(fields=['state_id', 'county_name'], name='uscity_state_abbrev_county_idx'), models.Index(fields=['state_id', 'city'], name='uscity_state_abbrev_city_idx'), models.Index(fields=['city', 'state_id'], name='uscity_city_state_search_idx'), models.Index(fields=['county_name', 'state_id'], name='uscity_county_state_search_idx'), models.Index(fields=['latitude', 'longitude'], name='uscity_coordinates_idx')],
                'constraints': [models.UniqueConstraint(fields=('city', 'county_name', 'state_name'), name='unique_city_county_state')],
            },
        ),
        migrations.CreateModel(
            name='Venue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('venue_name', models.CharField(help_text='Name of the venue/business', max_length=255, verbose_name='venue name')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated)', max_length=255, null=True, unique=True, verbose_name='slug')),
                ('short_description', models.TextField(help_text='Brief description of the venue (max 500 characters)', max_length=500, verbose_name='description')),
                ('state', models.CharField(choices=[('AL', 'Alabama'), ('AK', 'Alaska'), ('AZ', 'Arizona'), ('AR', 'Arkansas'), ('CA', 'California'), ('CO', 'Colorado'), ('CT', 'Connecticut'), ('DE', 'Delaware'), ('FL', 'Florida'), ('GA', 'Georgia'), ('HI', 'Hawaii'), ('ID', 'Idaho'), ('IL', 'Illinois'), ('IN', 'Indiana'), ('IA', 'Iowa'), ('KS', 'Kansas'), ('KY', 'Kentucky'), ('LA', 'Louisiana'), ('ME', 'Maine'), ('MD', 'Maryland'), ('MA', 'Massachusetts'), ('MI', 'Michigan'), ('MN', 'Minnesota'), ('MS', 'Mississippi'), ('MO', 'Missouri'), ('MT', 'Montana'), ('NE', 'Nebraska'), ('NV', 'Nevada'), ('NH', 'New Hampshire'), ('NJ', 'New Jersey'), ('NM', 'New Mexico'), ('NY', 'New York'), ('NC', 'North Carolina'), ('ND', 'North Dakota'), ('OH', 'Ohio'), ('OK', 'Oklahoma'), ('OR', 'Oregon'), ('PA', 'Pennsylvania'), ('RI', 'Rhode Island'), ('SC', 'South Carolina'), ('SD', 'South Dakota'), ('TN', 'Tennessee'), ('TX', 'Texas'), ('UT', 'Utah'), ('VT', 'Vermont'), ('VA', 'Virginia'), ('WA', 'Washington'), ('WV', 'West Virginia'), ('WI', 'Wisconsin'), ('WY', 'Wyoming')], help_text='State where venue is located', max_length=2, verbose_name='state')),
                ('county', models.CharField(help_text='County where the venue is located', max_length=100, verbose_name='county')),
                ('city', models.CharField(help_text='City where the venue is located', max_length=100, verbose_name='city')),
                ('street_number', models.CharField(help_text='Street number', max_length=20, verbose_name='street number')),
                ('street_name', models.CharField(help_text='Street name', max_length=255, verbose_name='street name')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, help_text='Latitude coordinate for mapping', max_digits=10, null=True, verbose_name='latitude')),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, help_text='Longitude coordinate for mapping', max_digits=11, null=True, verbose_name='longitude')),
                ('main_image', models.ImageField(blank=True, help_text='Main featured image for the venue (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression.', null=True, upload_to=venues_app.models.get_venue_main_image_path, verbose_name='main image')),
                ('phone', models.CharField(blank=True, help_text='Contact phone number for the venue', max_length=20, verbose_name='phone number')),
                ('email', models.EmailField(blank=True, help_text='Contact email address for the venue', max_length=254, verbose_name='email address')),
                ('website_url', models.URLField(blank=True, help_text='Official website URL for the venue', verbose_name='website URL')),
                ('instagram_url', models.URLField(blank=True, help_text='Instagram profile URL for the venue', verbose_name='Instagram URL')),
                ('facebook_url', models.URLField(blank=True, help_text='Facebook page URL for the venue', verbose_name='Facebook URL')),
                ('twitter_url', models.URLField(blank=True, help_text='Twitter profile URL for the venue', verbose_name='Twitter URL')),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn business page URL for the venue', verbose_name='LinkedIn URL')),
                ('email_verified', models.BooleanField(default=False, help_text='Whether the venue email address has been verified', verbose_name='email verified')),
                ('email_verification_token', models.CharField(blank=True, help_text='Token for email verification', max_length=64, verbose_name='email verification token')),
                ('operating_hours', models.TextField(blank=True, help_text='Operating hours (e.g., 9AM-5PM daily schedule)', max_length=500, verbose_name='operating hours')),
                ('opening_notes', models.TextField(blank=True, help_text='Custom notes about opening times', max_length=300, verbose_name='opening notes')),
                ('tags', models.CharField(blank=True, db_index=True, help_text='Keywords for search optimization (comma-separated)', max_length=255, verbose_name='tags')),
                ('search_vector', django.contrib.postgres.search.SearchVectorField(blank=True, editable=False, help_text='Search optimization field', null=True)),
                ('approval_status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='draft', help_text='Admin approval status', max_length=20, verbose_name='approval status')),
                ('visibility', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', help_text='Visibility to customers', max_length=20, verbose_name='visibility')),
                ('admin_notes', models.TextField(blank=True, help_text='Internal notes about approval/rejection', verbose_name='admin notes')),
                ('status_log', models.JSONField(default=list, editable=False, help_text='Audit log of status changes')),
                ('is_deleted', models.BooleanField(default=False, help_text='Mark as deleted instead of actual deletion', verbose_name='deleted status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('approved_at', models.DateTimeField(blank=True, help_text='When the venue was approved', null=True, verbose_name='approved at')),
                ('rejected_at', models.DateTimeField(blank=True, help_text='When the venue was rejected', null=True, verbose_name='rejected at')),
                ('last_modified_at', models.DateTimeField(auto_now=True, help_text='When the venue was last modified', verbose_name='last modified at')),
                ('show_contact_info', models.BooleanField(default=True, help_text='Show phone, email, and website to customers', verbose_name='show contact information')),
                ('show_operating_hours', models.BooleanField(default=True, help_text='Show opening hours to customers', verbose_name='show operating hours')),
                ('show_amenities', models.BooleanField(default=True, help_text='Show venue amenities and features', verbose_name='show amenities')),
                ('show_faqs', models.BooleanField(default=True, help_text='Show frequently asked questions', verbose_name='show FAQs')),
                ('show_team_members', models.BooleanField(default=True, help_text='Show service provider team members', verbose_name='show team members')),
                ('show_social_media', models.BooleanField(default=True, help_text='Show social media links', verbose_name='show social media')),
                ('description_updated_at', models.DateTimeField(blank=True, help_text='When venue description was last updated', null=True, verbose_name='description updated at')),
                ('contact_updated_at', models.DateTimeField(blank=True, help_text='When contact information was last updated', null=True, verbose_name='contact updated at')),
                ('hours_updated_at', models.DateTimeField(blank=True, help_text='When operating hours were last updated', null=True, verbose_name='hours updated at')),
                ('amenities_updated_at', models.DateTimeField(blank=True, help_text='When amenities were last updated', null=True, verbose_name='amenities updated at')),
                ('completeness_score', models.PositiveIntegerField(default=0, help_text='Calculated completeness score (0-100)', verbose_name='completeness score')),
                ('last_modified_by', models.ForeignKey(blank=True, help_text='User who last modified this venue', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_venues', to=settings.AUTH_USER_MODEL, verbose_name='last modified by')),
                ('service_provider', models.OneToOneField(help_text='Service provider who owns this venue', on_delete=django.db.models.deletion.CASCADE, related_name='venue', to='accounts_app.serviceproviderprofile', verbose_name='service provider')),
                ('us_city', models.ForeignKey(blank=True, help_text='Standardized location data', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='venues', to='venues_app.uscity', verbose_name='US city')),
            ],
            options={
                'verbose_name': 'Venue',
                'verbose_name_plural': 'Venues',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_title', models.CharField(help_text='Title/name of the service', max_length=255, verbose_name='service title')),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly slug (auto-generated or custom)', max_length=255, null=True, verbose_name='slug')),
                ('custom_slug', models.SlugField(blank=True, help_text='Custom URL slug (optional). Leave blank to auto-generate from title.', max_length=255, verbose_name='custom slug')),
                ('short_description', models.TextField(blank=True, help_text='Brief description of the service', max_length=500, verbose_name='description')),
                ('price_min', models.DecimalField(decimal_places=2, help_text='Minimum price for this service', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='minimum price')),
                ('price_max', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum price for variable pricing', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='maximum price')),
                ('duration_minutes', models.PositiveIntegerField(default=60, help_text='Duration of the service in minutes', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(1440)], verbose_name='duration (minutes)')),
                ('has_custom_availability', models.BooleanField(default=False, help_text='Whether this service has different availability from venue hours', verbose_name='custom availability')),
                ('requires_booking', models.BooleanField(default=True, help_text='Whether this service requires advance booking', verbose_name='requires booking')),
                ('max_advance_booking_days', models.PositiveIntegerField(default=60, help_text='Maximum days in advance customers can book', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)], verbose_name='max advance booking (days)')),
                ('min_advance_booking_hours', models.PositiveIntegerField(default=2, help_text='Minimum hours in advance required for booking', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)], verbose_name='min advance booking (hours)')),
                ('service_image', models.ImageField(blank=True, help_text='Optional image for this service (JPEG, PNG, WebP - max 2MB)', null=True, upload_to='services/images/', verbose_name='service image')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this service is bookable', verbose_name='active status')),
                ('is_featured', models.BooleanField(default=False, help_text='Whether this service should be highlighted', verbose_name='featured service')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('service_category', models.ForeignKey(blank=True, help_text='Category that best describes this service', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services', to='venues_app.servicecategory', verbose_name='service category')),
                ('tags', models.ManyToManyField(blank=True, help_text='Tags for better searchability (e.g., relaxing, deep-tissue, pain-relief)', related_name='services', to='venues_app.servicetag', verbose_name='service tags')),
                ('venue', models.ForeignKey(help_text='Venue where this service is offered', on_delete=django.db.models.deletion.CASCADE, related_name='services', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
                'ordering': ['service_title'],
            },
        ),
        migrations.CreateModel(
            name='OperatingHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.PositiveSmallIntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')], help_text='Day of the week (0=Monday, 6=Sunday)', verbose_name='day of week')),
                ('opening', models.TimeField(blank=True, help_text='Opening time for this day', null=True, verbose_name='opening time')),
                ('closing', models.TimeField(blank=True, help_text='Closing time for this day', null=True, verbose_name='closing time')),
                ('is_closed', models.BooleanField(default=False, help_text='Whether the venue is closed on this day', verbose_name='closed')),
                ('is_24_hours', models.BooleanField(default=False, help_text='Whether the venue is open 24 hours on this day', verbose_name='24 hours')),
                ('is_overnight', models.BooleanField(default=False, help_text='Whether closing time is next day (e.g., open until 2 AM)', verbose_name='overnight hours')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue these hours belong to', on_delete=django.db.models.deletion.CASCADE, related_name='operating_hours_set', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Operating Hours',
                'verbose_name_plural': 'Operating Hours',
                'ordering': ['day'],
            },
        ),
        migrations.CreateModel(
            name='HolidaySchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the holiday or special event', max_length=100, verbose_name='holiday name')),
                ('date', models.DateField(help_text='Date of the special schedule', verbose_name='date')),
                ('opening', models.TimeField(blank=True, help_text='Opening time for this special day', null=True, verbose_name='opening time')),
                ('closing', models.TimeField(blank=True, help_text='Closing time for this special day', null=True, verbose_name='closing time')),
                ('is_closed', models.BooleanField(default=False, help_text='Whether the venue is closed on this special day', verbose_name='closed')),
                ('is_24_hours', models.BooleanField(default=False, help_text='Whether the venue is open 24 hours on this special day', verbose_name='24 hours')),
                ('is_overnight', models.BooleanField(default=False, help_text='Whether closing time is next day', verbose_name='overnight hours')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this special schedule', verbose_name='notes')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this special schedule is active', verbose_name='active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='holiday_schedules', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Holiday Schedule',
                'verbose_name_plural': 'Holiday Schedules',
                'ordering': ['date'],
            },
        ),
        migrations.CreateModel(
            name='FlaggedVenue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.TextField(help_text='Reason for flagging this venue', max_length=1000, verbose_name='reason')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('reviewed', 'Reviewed'), ('resolved', 'Resolved')], default='pending', help_text='Status of the flag review', max_length=20, verbose_name='status')),
                ('admin_notes', models.TextField(blank=True, help_text='Admin notes about the flag review', verbose_name='admin notes')),
                ('reviewed_at', models.DateTimeField(blank=True, help_text='When the flag was reviewed', null=True, verbose_name='reviewed at')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('flagged_by', models.ForeignKey(help_text='Customer who flagged this venue', on_delete=django.db.models.deletion.CASCADE, related_name='flagged_venues', to=settings.AUTH_USER_MODEL, verbose_name='flagged by')),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Admin who reviewed this flag', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_flags', to=settings.AUTH_USER_MODEL, verbose_name='reviewed by')),
                ('venue', models.ForeignKey(help_text='Venue that has been flagged', on_delete=django.db.models.deletion.CASCADE, related_name='flags', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Flagged Venue',
                'verbose_name_plural': 'Flagged Venues',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VenueAmenity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amenity_type', models.CharField(choices=[('wifi', 'Wi-Fi'), ('parking', 'Parking Available'), ('wheelchair_accessible', 'Wheelchair Accessible'), ('air_conditioning', 'Air Conditioning'), ('heating', 'Heating'), ('music', 'Background Music'), ('private_rooms', 'Private Treatment Rooms'), ('changing_rooms', 'Changing Rooms'), ('shower_facilities', 'Shower Facilities'), ('refreshments', 'Refreshments Available'), ('retail_shop', 'Retail Shop'), ('online_booking', 'Online Booking'), ('credit_cards', 'Credit Cards Accepted'), ('gift_certificates', 'Gift Certificates'), ('loyalty_program', 'Loyalty Program')], help_text='Type of amenity or feature', max_length=50, verbose_name='amenity type')),
                ('custom_name', models.CharField(blank=True, help_text='Custom name for the amenity (optional)', max_length=100, verbose_name='custom name')),
                ('description', models.TextField(blank=True, help_text='Additional details about this amenity', max_length=200, verbose_name='description')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this amenity is currently available', verbose_name='active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue this amenity belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='amenities', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Venue Amenity',
                'verbose_name_plural': 'Venue Amenities',
                'ordering': ['amenity_type'],
            },
        ),
        migrations.CreateModel(
            name='VenueCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='category_venues', to='venues_app.category', verbose_name='category')),
                ('venue', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='venue_categories', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Venue Category',
                'verbose_name_plural': 'Venue Categories',
            },
        ),
        migrations.AddField(
            model_name='venue',
            name='categories',
            field=models.ManyToManyField(blank=True, help_text='Categories this venue belongs to', related_name='venues', through='venues_app.VenueCategory', to='venues_app.category', verbose_name='categories'),
        ),
        migrations.CreateModel(
            name='VenueCreationDraft',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('venue_name', models.CharField(blank=True, help_text='Name of the venue/business', max_length=255, null=True, verbose_name='venue name')),
                ('short_description', models.TextField(blank=True, help_text='Brief description of the venue', max_length=500, null=True, verbose_name='description')),
                ('state', models.CharField(blank=True, choices=[('AL', 'Alabama'), ('AK', 'Alaska'), ('AZ', 'Arizona'), ('AR', 'Arkansas'), ('CA', 'California'), ('CO', 'Colorado'), ('CT', 'Connecticut'), ('DE', 'Delaware'), ('FL', 'Florida'), ('GA', 'Georgia'), ('HI', 'Hawaii'), ('ID', 'Idaho'), ('IL', 'Illinois'), ('IN', 'Indiana'), ('IA', 'Iowa'), ('KS', 'Kansas'), ('KY', 'Kentucky'), ('LA', 'Louisiana'), ('ME', 'Maine'), ('MD', 'Maryland'), ('MA', 'Massachusetts'), ('MI', 'Michigan'), ('MN', 'Minnesota'), ('MS', 'Mississippi'), ('MO', 'Missouri'), ('MT', 'Montana'), ('NE', 'Nebraska'), ('NV', 'Nevada'), ('NH', 'New Hampshire'), ('NJ', 'New Jersey'), ('NM', 'New Mexico'), ('NY', 'New York'), ('NC', 'North Carolina'), ('ND', 'North Dakota'), ('OH', 'Ohio'), ('OK', 'Oklahoma'), ('OR', 'Oregon'), ('PA', 'Pennsylvania'), ('RI', 'Rhode Island'), ('SC', 'South Carolina'), ('SD', 'South Dakota'), ('TN', 'Tennessee'), ('TX', 'Texas'), ('UT', 'Utah'), ('VT', 'Vermont'), ('VA', 'Virginia'), ('WA', 'Washington'), ('WV', 'West Virginia'), ('WI', 'Wisconsin'), ('WY', 'Wyoming')], help_text='State where venue is located', max_length=2, null=True, verbose_name='state')),
                ('county', models.CharField(blank=True, help_text='County where venue is located', max_length=100, null=True, verbose_name='county')),
                ('city', models.CharField(blank=True, help_text='City where venue is located', max_length=100, null=True, verbose_name='city')),
                ('street_number', models.CharField(blank=True, help_text='Street number', max_length=20, null=True, verbose_name='street number')),
                ('street_name', models.CharField(blank=True, help_text='Street name', max_length=255, null=True, verbose_name='street name')),
                ('phone', models.CharField(blank=True, help_text='Contact phone number', max_length=20, null=True, verbose_name='phone')),
                ('email', models.EmailField(blank=True, help_text='Contact email address', max_length=254, null=True, verbose_name='email')),
                ('website_url', models.URLField(blank=True, help_text='Website URL', null=True, verbose_name='website URL')),
                ('instagram_url', models.URLField(blank=True, help_text='Instagram profile URL', null=True, verbose_name='Instagram URL')),
                ('facebook_url', models.URLField(blank=True, help_text='Facebook page URL', null=True, verbose_name='Facebook URL')),
                ('twitter_url', models.URLField(blank=True, help_text='Twitter profile URL', null=True, verbose_name='Twitter URL')),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile URL', null=True, verbose_name='LinkedIn URL')),
                ('categories_data', models.JSONField(blank=True, default=list, help_text='Selected category IDs stored as JSON', verbose_name='categories data')),
                ('operating_hours_data', models.JSONField(blank=True, default=dict, help_text='Operating hours for each day stored as JSON', verbose_name='operating hours data')),
                ('amenities_data', models.JSONField(blank=True, default=list, help_text='Selected amenity types stored as JSON', verbose_name='amenities data')),
                ('services_data', models.JSONField(blank=True, default=list, help_text='Services with pricing and discount information stored as JSON', verbose_name='services data')),
                ('team_members_data', models.JSONField(blank=True, default=list, help_text='Team member information stored as JSON', verbose_name='team members data')),
                ('faqs_data', models.JSONField(blank=True, default=list, help_text='Frequently asked questions stored as JSON', verbose_name='FAQs data')),
                ('images_data', models.JSONField(blank=True, default=list, help_text='Image information and ordering stored as JSON', verbose_name='images data')),
                ('cancellation_policy', models.TextField(blank=True, help_text='Venue cancellation policy', max_length=1000, null=True, verbose_name='cancellation policy')),
                ('booking_policy', models.TextField(blank=True, help_text='Venue booking policy', max_length=1000, null=True, verbose_name='booking policy')),
                ('special_instructions', models.TextField(blank=True, help_text='Special instructions for customers', max_length=500, null=True, verbose_name='special instructions')),
                ('zip_code', models.CharField(blank=True, help_text='Postal/ZIP code', max_length=10, null=True, verbose_name='zip code')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, help_text='Latitude coordinate', max_digits=10, null=True, verbose_name='latitude')),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, help_text='Longitude coordinate', max_digits=11, null=True, verbose_name='longitude')),
                ('current_step', models.CharField(default='basic', help_text='Current wizard step', max_length=20, verbose_name='current step')),
                ('completed_steps', models.JSONField(blank=True, default=list, help_text='List of completed wizard steps', verbose_name='completed steps')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the draft was first created', verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the draft was last updated', verbose_name='updated at')),
                ('service_provider', models.OneToOneField(help_text='Service provider creating the venue', on_delete=django.db.models.deletion.CASCADE, related_name='venue_creation_draft', to='accounts_app.serviceproviderprofile', verbose_name='service provider')),
            ],
            options={
                'verbose_name': 'venue creation draft',
                'verbose_name_plural': 'venue creation drafts',
                'db_table': 'venues_venue_creation_draft',
            },
        ),
        migrations.CreateModel(
            name='VenueFAQ',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.CharField(help_text='Frequently asked question', max_length=255, verbose_name='question')),
                ('answer', models.TextField(help_text='Answer to the question (max 500 characters)', max_length=500, verbose_name='answer')),
                ('order', models.PositiveIntegerField(default=1, help_text='Display order of the FAQ (1-5)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='display order')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this FAQ is visible', verbose_name='active status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue this FAQ belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='faqs', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Venue FAQ',
                'verbose_name_plural': 'Venue FAQs',
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='VenueImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Venue image file (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression.', upload_to=venues_app.models.get_venue_gallery_image_path, verbose_name='image')),
                ('order', models.PositiveIntegerField(blank=True, help_text='Display order of the image (1-5)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='display order')),
                ('caption', models.CharField(blank=True, help_text='Optional caption for the image', max_length=255, verbose_name='caption')),
                ('is_primary', models.BooleanField(default=False, help_text='Whether this is the primary image for the venue', verbose_name='primary image')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this image is visible', verbose_name='active status')),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='Image file size in bytes', null=True, verbose_name='file size')),
                ('width', models.PositiveIntegerField(blank=True, help_text='Image width in pixels', null=True, verbose_name='image width')),
                ('height', models.PositiveIntegerField(blank=True, help_text='Image height in pixels', null=True, verbose_name='image height')),
                ('format', models.CharField(blank=True, help_text='Image file format (JPEG, PNG, WebP)', max_length=10, verbose_name='image format')),
                ('original_filename', models.CharField(blank=True, help_text='Original filename when uploaded', max_length=255, verbose_name='original filename')),
                ('upload_session_id', models.CharField(blank=True, help_text='Session ID for tracking upload batches', max_length=100, verbose_name='upload session ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('venue', models.ForeignKey(help_text='Venue this image belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='images', to='venues_app.venue', verbose_name='venue')),
            ],
            options={
                'verbose_name': 'Venue Image',
                'verbose_name_plural': 'Venue Images',
                'ordering': ['-is_primary', 'order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='ServiceOperatingHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.PositiveSmallIntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')], help_text='Day of the week (0=Monday, 6=Sunday)', verbose_name='day of week')),
                ('opening', models.TimeField(blank=True, help_text='Service available from this time', null=True, verbose_name='opening time')),
                ('closing', models.TimeField(blank=True, help_text='Service available until this time', null=True, verbose_name='closing time')),
                ('is_closed', models.BooleanField(default=False, help_text='Whether the service is unavailable on this day', verbose_name='closed')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('service', models.ForeignKey(help_text='Service these hours apply to', on_delete=django.db.models.deletion.CASCADE, related_name='service_hours', to='venues_app.service', verbose_name='service')),
            ],
            options={
                'verbose_name': 'Service Operating Hours',
                'verbose_name_plural': 'Service Operating Hours',
                'ordering': ['day'],
                'unique_together': {('service', 'day')},
            },
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['service_title'], name='venues_app__service_7a8165_idx'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['is_active'], name='venues_app__is_acti_e1bdc5_idx'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['service_category'], name='venues_app__service_4307fe_idx'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['is_featured'], name='venues_app__is_feat_eb3022_idx'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['venue', 'slug'], name='venues_app__venue_i_bf62a6_idx'),
        ),
        migrations.AddIndex(
            model_name='service',
            index=models.Index(fields=['venue', 'is_active'], name='venues_app__venue_i_896798_idx'),
        ),
        migrations.AddConstraint(
            model_name='service',
            constraint=models.UniqueConstraint(condition=models.Q(('slug__isnull', False)), fields=('venue', 'slug'), name='unique_service_slug_per_venue'),
        ),
        migrations.AlterUniqueTogether(
            name='operatinghours',
            unique_together={('venue', 'day')},
        ),
        migrations.AlterUniqueTogether(
            name='holidayschedule',
            unique_together={('venue', 'date')},
        ),
        migrations.AddIndex(
            model_name='flaggedvenue',
            index=models.Index(fields=['status'], name='venues_app__status_92eadd_idx'),
        ),
        migrations.AddIndex(
            model_name='flaggedvenue',
            index=models.Index(fields=['created_at'], name='venues_app__created_3f43c5_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='flaggedvenue',
            unique_together={('venue', 'flagged_by')},
        ),
        migrations.AlterUniqueTogether(
            name='venueamenity',
            unique_together={('venue', 'amenity_type')},
        ),
        migrations.AlterUniqueTogether(
            name='venuecategory',
            unique_together={('venue', 'category')},
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['approval_status', 'visibility'], name='venues_app__approva_48ce4d_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['state', 'county', 'city'], name='venues_app__state_5d5c7d_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['latitude', 'longitude'], name='venues_app__latitud_34f5b0_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['created_at'], name='venues_app__created_9391a8_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['tags'], name='venues_app__tags_4ba727_idx'),
        ),
        migrations.AddIndex(
            model_name='venue',
            index=models.Index(fields=['service_provider', 'is_deleted'], name='venues_app__service_78e600_idx'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.UniqueConstraint(condition=models.Q(('is_deleted', False)), fields=('service_provider',), name='unique_venue_per_service_provider'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.UniqueConstraint(condition=models.Q(('is_deleted', False)), fields=('venue_name', 'city', 'state'), name='unique_venue_name_per_location'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('approval_status__in', ['draft', 'pending', 'rejected']), ('approved_at__isnull', False), _connector='OR'), name='approved_venues_have_timestamp'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('approval_status__in', ['draft', 'pending', 'approved']), ('rejected_at__isnull', False), _connector='OR'), name='rejected_venues_have_timestamp'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('latitude__isnull', True), models.Q(('latitude__gte', -90), ('latitude__lte', 90)), _connector='OR'), name='valid_latitude_range'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('longitude__isnull', True), models.Q(('longitude__gte', -180), ('longitude__lte', 180)), _connector='OR'), name='valid_longitude_range'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('venue_name', ''), _negated=True), name='venue_name_not_empty'),
        ),
        migrations.AddConstraint(
            model_name='venue',
            constraint=models.CheckConstraint(condition=models.Q(('short_description', ''), _negated=True), name='venue_description_not_empty'),
        ),
        migrations.AlterUniqueTogether(
            name='venuefaq',
            unique_together={('venue', 'order')},
        ),
        migrations.AddConstraint(
            model_name='venueimage',
            constraint=models.UniqueConstraint(deferrable=django.db.models.constraints.Deferrable['DEFERRED'], fields=('venue', 'order'), name='unique_venue_image_order'),
        ),
        migrations.AddConstraint(
            model_name='venueimage',
            constraint=models.UniqueConstraint(condition=models.Q(('is_primary', True)), fields=('venue',), name='unique_primary_image_per_venue'),
        ),
    ]
