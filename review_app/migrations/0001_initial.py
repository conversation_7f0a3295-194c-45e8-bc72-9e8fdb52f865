# Generated by Django 5.2.4 on 2025-07-05 17:30

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('venues_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveSmallIntegerField(choices=[(1, '1 Star - Poor'), (2, '2 Stars - Fair'), (3, '3 Stars - Good'), (4, '4 Stars - Very Good'), (5, '5 Stars - Excellent')], help_text='Star rating from 1 to 5', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('written_review', models.TextField(blank=True, help_text='Written review content (max 1000 characters, optional)', max_length=1000)),
                ('slug', models.SlugField(blank=True, help_text='Unique slug for sharing this review', max_length=64, unique=True)),
                ('is_approved', models.BooleanField(default=True, help_text='Whether this review is approved and visible')),
                ('is_flagged', models.BooleanField(default=False, help_text='Whether this review has been flagged for review')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the review was posted')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the review was last updated')),
                ('customer', models.ForeignKey(help_text='Customer who wrote this review', on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to=settings.AUTH_USER_MODEL)),
                ('venue', models.ForeignKey(help_text='Venue being reviewed', on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='venues_app.venue')),
            ],
            options={
                'verbose_name': 'Review',
                'verbose_name_plural': 'Reviews',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReviewDraft',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveSmallIntegerField(blank=True, help_text='Star rating from 1 to 5 (optional in draft)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('written_review', models.TextField(blank=True, help_text='Written review content (max 1000 characters, optional)', max_length=1000)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the draft was first created')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the draft was last updated')),
                ('customer', models.ForeignKey(help_text='Customer who created this draft', on_delete=django.db.models.deletion.CASCADE, related_name='review_drafts', to=settings.AUTH_USER_MODEL)),
                ('venue', models.ForeignKey(help_text='Venue being reviewed', on_delete=django.db.models.deletion.CASCADE, related_name='review_drafts', to='venues_app.venue')),
            ],
            options={
                'verbose_name': 'Review Draft',
                'verbose_name_plural': 'Review Drafts',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='ReviewFlag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reason', models.CharField(choices=[('inappropriate', 'Inappropriate Content'), ('fake', 'Fake Review'), ('spam', 'Spam'), ('offensive', 'Offensive Language'), ('other', 'Other')], help_text='Reason for flagging this review', max_length=20)),
                ('reason_text', models.TextField(blank=True, help_text='Additional details about why this review was flagged', max_length=500)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('reviewed', 'Reviewed'), ('resolved', 'Resolved')], default='pending', help_text='Current status of this flag', max_length=20)),
                ('admin_notes', models.TextField(blank=True, help_text='Admin notes about the flag resolution')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the flag was created')),
                ('reviewed_at', models.DateTimeField(blank=True, help_text='When the flag was reviewed by admin', null=True)),
                ('flagged_by', models.ForeignKey(help_text='Customer who flagged this review', on_delete=django.db.models.deletion.CASCADE, related_name='review_flags', to=settings.AUTH_USER_MODEL)),
                ('review', models.ForeignKey(help_text='Review being flagged', on_delete=django.db.models.deletion.CASCADE, related_name='flags', to='review_app.review')),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Admin who reviewed this flag', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_review_flags', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Review Flag',
                'verbose_name_plural': 'Review Flags',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReviewHelpfulness',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_helpful', models.BooleanField(help_text='True if user found review helpful, False if not helpful')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the vote was cast')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the vote was last updated')),
                ('review', models.ForeignKey(help_text='Review being voted on', on_delete=django.db.models.deletion.CASCADE, related_name='helpfulness_votes', to='review_app.review')),
                ('user', models.ForeignKey(help_text='User who voted on helpfulness', on_delete=django.db.models.deletion.CASCADE, related_name='review_helpfulness_votes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Review Helpfulness Vote',
                'verbose_name_plural': 'Review Helpfulness Votes',
            },
        ),
        migrations.CreateModel(
            name='ReviewResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response_text', models.TextField(help_text='Provider response to the review (max 500 characters)', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the response was posted')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the response was last updated')),
                ('provider', models.ForeignKey(help_text='Service provider who wrote this response', on_delete=django.db.models.deletion.CASCADE, related_name='review_responses', to=settings.AUTH_USER_MODEL)),
                ('review', models.OneToOneField(help_text='Review being responded to', on_delete=django.db.models.deletion.CASCADE, related_name='response', to='review_app.review')),
            ],
            options={
                'verbose_name': 'Review Response',
                'verbose_name_plural': 'Review Responses',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomerReviewResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response_text', models.TextField(help_text='Customer response to provider response (max 300 characters)', max_length=300)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When the response was posted')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When the response was last updated')),
                ('customer', models.ForeignKey(help_text='Customer who wrote this response', on_delete=django.db.models.deletion.CASCADE, related_name='customer_review_responses', to=settings.AUTH_USER_MODEL)),
                ('provider_response', models.OneToOneField(help_text='Provider response being responded to', on_delete=django.db.models.deletion.CASCADE, related_name='customer_response', to='review_app.reviewresponse')),
            ],
            options={
                'verbose_name': 'Customer Review Response',
                'verbose_name_plural': 'Customer Review Responses',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['rating'], name='review_app__rating_7210ed_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['created_at'], name='review_app__created_cd94ef_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['venue'], name='review_app__venue_i_703b73_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='review',
            unique_together={('customer', 'venue')},
        ),
        migrations.AddIndex(
            model_name='reviewdraft',
            index=models.Index(fields=['customer'], name='review_app__custome_aa9086_idx'),
        ),
        migrations.AddIndex(
            model_name='reviewdraft',
            index=models.Index(fields=['venue'], name='review_app__venue_i_c7bcba_idx'),
        ),
        migrations.AddIndex(
            model_name='reviewdraft',
            index=models.Index(fields=['updated_at'], name='review_app__updated_ab7c4c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='reviewdraft',
            unique_together={('customer', 'venue')},
        ),
        migrations.AlterUniqueTogether(
            name='reviewflag',
            unique_together={('review', 'flagged_by')},
        ),
        migrations.AddIndex(
            model_name='reviewhelpfulness',
            index=models.Index(fields=['review', 'is_helpful'], name='review_app__review__3cbfed_idx'),
        ),
        migrations.AddIndex(
            model_name='reviewhelpfulness',
            index=models.Index(fields=['user'], name='review_app__user_id_19077a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='reviewhelpfulness',
            unique_together={('review', 'user')},
        ),
    ]
