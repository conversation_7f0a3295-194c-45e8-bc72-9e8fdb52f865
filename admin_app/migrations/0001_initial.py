# Generated by Django 5.2.4 on 2025-07-05 17:30

import admin_app.models
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BlogCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Category name', max_length=100, unique=True)),
                ('slug', models.SlugField(help_text='URL-friendly version of the name (auto-generated)', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional description of the category')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this category is available for new posts')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Blog Category',
                'verbose_name_plural': 'Blog Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Announcement title', max_length=200, unique=True)),
                ('slug', models.SlugField(blank=True, help_text='URL-friendly version of the title (auto-generated)', max_length=200, unique=True)),
                ('content', models.TextField(help_text='Announcement content/message')),
                ('announcement_type', models.CharField(choices=[('info', 'Information'), ('success', 'Success'), ('warning', 'Warning'), ('danger', 'Danger'), ('promotion', 'Promotion')], default='info', help_text='Type of announcement (affects styling)', max_length=20)),
                ('display_location', models.CharField(choices=[('top_banner', 'Top Banner'), ('homepage', 'Homepage'), ('dashboard', 'Dashboard'), ('all_pages', 'All Pages')], default='top_banner', help_text='Where to display this announcement', max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this announcement is currently active')),
                ('is_dismissible', models.BooleanField(default=True, help_text='Whether users can dismiss this announcement')),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now, help_text='When to start displaying this announcement')),
                ('end_date', models.DateTimeField(blank=True, help_text='When to stop displaying this announcement (optional)', null=True)),
                ('target_user_roles', models.CharField(blank=True, help_text='Comma-separated user roles to show this to (empty = all users)', max_length=100)),
                ('priority', models.PositiveIntegerField(default=0, help_text='Display priority (higher numbers shown first)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_announcements', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_announcements', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Announcement',
                'verbose_name_plural': 'Announcements',
                'ordering': ['-priority', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BlogPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Blog post title', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly version of the title (auto-generated)', max_length=200, unique=True)),
                ('content', models.TextField(help_text='Rich content with support for HTML, images, and videos')),
                ('excerpt', models.TextField(blank=True, help_text='Short description for previews (max 300 characters)', max_length=300)),
                ('featured_image', models.ImageField(blank=True, help_text='Featured image for the blog post (JPG/PNG only, max 5MB)', null=True, upload_to=admin_app.models.get_blog_featured_image_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png'])])),
                ('meta_title', models.CharField(blank=True, help_text='SEO title tag (max 60 characters, auto-generated if empty)', max_length=60)),
                ('meta_description', models.CharField(blank=True, help_text='SEO meta description (max 160 characters, auto-generated if empty)', max_length=160)),
                ('meta_keywords', models.CharField(blank=True, help_text='SEO keywords, comma-separated', max_length=255)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('archived', 'Archived')], default='draft', help_text='Publication status of the post', max_length=20)),
                ('is_featured', models.BooleanField(default=False, help_text='Display this post prominently on the blog page')),
                ('published_at', models.DateTimeField(blank=True, help_text='Date and time when the post was published', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='authored_blog_posts', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(blank=True, help_text='Blog post category', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='blog_posts', to='admin_app.blogcategory')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_blog_posts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Blog Post',
                'verbose_name_plural': 'Blog Posts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BulkActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('user_activation', 'User Activation'), ('user_deactivation', 'User Deactivation'), ('user_deletion', 'User Deletion'), ('provider_approval', 'Provider Approval'), ('provider_rejection', 'Provider Rejection'), ('content_publish', 'Content Publishing'), ('content_unpublish', 'Content Unpublishing'), ('content_deletion', 'Content Deletion'), ('other', 'Other Action')], help_text='Type of bulk action performed', max_length=50)),
                ('description', models.TextField(help_text='Detailed description of the action performed')),
                ('affected_count', models.PositiveIntegerField(default=0, help_text='Number of items affected by this action')),
                ('affected_model', models.CharField(blank=True, help_text='Model name of affected items (e.g., CustomUser, BlogPost)', max_length=100)),
                ('affected_ids', models.TextField(blank=True, help_text='Comma-separated list of affected item IDs')),
                ('executed_at', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address from which the action was executed', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent string')),
                ('executed_by', models.ForeignKey(help_text='Admin user who executed this action', on_delete=django.db.models.deletion.CASCADE, related_name='executed_bulk_actions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Bulk Action Log',
                'verbose_name_plural': 'Bulk Action Logs',
                'ordering': ['-executed_at'],
            },
        ),
        migrations.CreateModel(
            name='HomepageBlock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('block_type', models.CharField(choices=[('hero', 'Hero Section'), ('how_it_works', 'How It Works'), ('top_deals', 'Top Deals'), ('testimonials', 'Testimonials'), ('features', 'Features'), ('call_to_action', 'Call to Action'), ('custom', 'Custom Block')], help_text='Type of homepage block', max_length=50, unique=True)),
                ('title', models.CharField(help_text='Block title/heading', max_length=200)),
                ('subtitle', models.CharField(blank=True, help_text='Optional subtitle or description', max_length=300)),
                ('content', models.TextField(blank=True, help_text='Rich content for the block')),
                ('image', models.ImageField(blank=True, help_text='Block image (JPG/PNG only, max 5MB)', null=True, upload_to=admin_app.models.get_homepage_block_image_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png'])])),
                ('button_text', models.CharField(blank=True, help_text='Text for call-to-action button', max_length=100)),
                ('button_url', models.URLField(blank=True, help_text='URL for call-to-action button')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this block is displayed on the homepage')),
                ('display_order', models.PositiveIntegerField(default=0, help_text='Order in which blocks appear on the homepage')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_homepage_blocks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Homepage Block',
                'verbose_name_plural': 'Homepage Blocks',
                'ordering': ['display_order', 'block_type'],
            },
        ),
        migrations.CreateModel(
            name='MediaFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Descriptive title for the media file', max_length=200)),
                ('file', models.FileField(help_text='Upload media file (images, documents, videos)', upload_to=admin_app.models.get_media_file_path)),
                ('file_type', models.CharField(choices=[('image', 'Image'), ('document', 'Document'), ('video', 'Video'), ('other', 'Other')], default='image', help_text='Type of media file', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Optional description of the media file')),
                ('alt_text', models.CharField(blank=True, help_text='Alternative text for images (accessibility)', max_length=255)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags for organizing media files', max_length=500)),
                ('is_public', models.BooleanField(default=True, help_text='Whether this file can be accessed publicly')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_media_files', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Media File',
                'verbose_name_plural': 'Media Files',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SiteConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='CozyWish', help_text='Name of the website', max_length=100)),
                ('site_tagline', models.CharField(blank=True, help_text='Short tagline or slogan for the site', max_length=200)),
                ('site_description', models.TextField(blank=True, help_text='Description of the website for SEO')),
                ('contact_email', models.EmailField(default='<EMAIL>', help_text='Primary contact email address', max_length=254)),
                ('contact_phone', models.CharField(blank=True, help_text='Primary contact phone number', max_length=20)),
                ('contact_address', models.TextField(blank=True, help_text='Physical address of the business')),
                ('facebook_url', models.URLField(blank=True, help_text='Facebook page URL')),
                ('twitter_url', models.URLField(blank=True, help_text='Twitter profile URL')),
                ('instagram_url', models.URLField(blank=True, help_text='Instagram profile URL')),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile URL')),
                ('default_meta_title', models.CharField(blank=True, help_text='Default meta title for pages (max 60 characters)', max_length=60)),
                ('default_meta_description', models.CharField(blank=True, help_text='Default meta description for pages (max 160 characters)', max_length=160)),
                ('default_meta_keywords', models.CharField(blank=True, help_text='Default meta keywords, comma-separated', max_length=255)),
                ('google_analytics_id', models.CharField(blank=True, help_text='Google Analytics tracking ID (e.g., GA-XXXXXXXXX-X)', max_length=50)),
                ('facebook_pixel_id', models.CharField(blank=True, help_text='Facebook Pixel ID for tracking', max_length=50)),
                ('maintenance_mode', models.BooleanField(default=False, help_text='Enable maintenance mode to show maintenance page to visitors')),
                ('maintenance_message', models.TextField(blank=True, help_text='Message to display during maintenance mode')),
                ('allow_user_registration', models.BooleanField(default=True, help_text='Allow new users to register on the site')),
                ('require_email_verification', models.BooleanField(default=True, help_text='Require email verification for new user accounts')),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='site_config_updates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Site Configuration',
                'verbose_name_plural': 'Site Configuration',
            },
        ),
        migrations.CreateModel(
            name='StaticPage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Page title displayed in browser and navigation', max_length=200)),
                ('slug', models.SlugField(help_text='URL-friendly version of the title (auto-generated)', max_length=200, unique=True)),
                ('content', models.TextField(help_text='Rich content with support for HTML, images, and videos')),
                ('meta_title', models.CharField(blank=True, help_text='SEO title tag (max 60 characters, auto-generated if empty)', max_length=60)),
                ('meta_description', models.CharField(blank=True, help_text='SEO meta description (max 160 characters, auto-generated if empty)', max_length=160)),
                ('meta_keywords', models.CharField(blank=True, help_text='SEO keywords, comma-separated', max_length=255)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('archived', 'Archived')], default='draft', help_text='Publication status of the page', max_length=20)),
                ('featured_image', models.ImageField(blank=True, help_text='Featured image for the page (JPG/PNG only, max 5MB)', null=True, upload_to=admin_app.models.get_static_page_media_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png'])])),
                ('is_featured', models.BooleanField(default=False, help_text='Display this page prominently in navigation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_static_pages', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_static_pages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Static Page',
                'verbose_name_plural': 'Static Pages',
                'ordering': ['title'],
            },
        ),
        migrations.CreateModel(
            name='SystemHealthLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('error', 'Error'), ('warning', 'Warning'), ('info', 'Information'), ('login_attempt', 'Login Attempt'), ('failed_login', 'Failed Login'), ('security_alert', 'Security Alert'), ('uptime', 'System Uptime'), ('downtime', 'System Downtime'), ('performance', 'Performance Issue'), ('maintenance', 'Maintenance')], help_text='Type of system event', max_length=50)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='low', help_text='Severity level of the event', max_length=20)),
                ('title', models.CharField(help_text='Brief title describing the event', max_length=200)),
                ('description', models.TextField(help_text='Detailed description of the event')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address related to the event', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent string')),
                ('additional_data', models.JSONField(blank=True, default=dict, help_text='Additional event data in JSON format')),
                ('is_resolved', models.BooleanField(default=False, help_text='Whether this event has been resolved')),
                ('resolved_at', models.DateTimeField(blank=True, help_text='When this event was resolved', null=True)),
                ('resolution_notes', models.TextField(blank=True, help_text='Notes about how the event was resolved')),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('affected_user', models.ForeignKey(blank=True, help_text='User affected by this event (if applicable)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='system_health_events', to=settings.AUTH_USER_MODEL)),
                ('resolved_by', models.ForeignKey(blank=True, help_text='Admin who resolved this event', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_system_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Health Log',
                'verbose_name_plural': 'System Health Logs',
                'ordering': ['-recorded_at'],
            },
        ),
    ]
