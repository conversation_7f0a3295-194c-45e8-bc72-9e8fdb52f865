# Generated by Django 5.2.4 on 2025-07-05 17:30

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('booking_cart_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique payment identifier', unique=True)),
                ('amount_paid', models.DecimalField(decimal_places=2, help_text='Total amount paid by customer', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('payment_method', models.CharField(choices=[('credit_card', 'Credit Card'), ('debit_card', 'Debit Card'), ('stripe', 'Stripe')], default='stripe', help_text='Payment method used', max_length=20)),
                ('stripe_payment_intent_id', models.CharField(blank=True, help_text='Stripe Payment Intent ID (placeholder for future implementation)', max_length=255, null=True)),
                ('stripe_charge_id', models.CharField(blank=True, help_text='Stripe Charge ID (placeholder for future implementation)', max_length=255, null=True)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('requires_action', 'Requires Action'), ('refunded', 'Refunded'), ('partially_refunded', 'Partially Refunded')], default='pending', help_text='Current status of the payment', max_length=20)),
                ('payment_date', models.DateTimeField(auto_now_add=True, help_text='When the payment was initiated')),
                ('completed_date', models.DateTimeField(blank=True, help_text='When the payment was completed', null=True)),
                ('failure_reason', models.TextField(blank=True, help_text='Reason for payment failure (if applicable)')),
                ('refunded_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total amount refunded', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('booking', models.ForeignKey(help_text='Booking this payment is for', on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='booking_cart_app.booking')),
                ('customer', models.ForeignKey(help_text='Customer who made the payment', on_delete=django.db.models.deletion.CASCADE, related_name='payments', to=settings.AUTH_USER_MODEL)),
                ('provider', models.ForeignKey(help_text='Service provider receiving the payment', limit_choices_to={'role': 'service_provider'}, on_delete=django.db.models.deletion.CASCADE, related_name='received_payments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='RefundRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('refund_request_id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique refund request identifier', unique=True)),
                ('reason_category', models.CharField(choices=[('service_not_provided', 'Service Not Provided'), ('poor_service_quality', 'Poor Service Quality'), ('booking_cancelled', 'Booking Cancelled'), ('technical_issue', 'Technical Issue'), ('other', 'Other')], help_text='Category of refund reason', max_length=30)),
                ('reason_description', models.TextField(help_text='Detailed description of refund reason', max_length=1000)),
                ('requested_amount', models.DecimalField(decimal_places=2, help_text='Amount requested for refund', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('request_status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('declined', 'Declined'), ('processed', 'Processed')], default='pending', help_text='Current status of refund request', max_length=20)),
                ('admin_notes', models.TextField(blank=True, help_text='Admin notes regarding the refund request')),
                ('reviewed_at', models.DateTimeField(blank=True, help_text='When the request was reviewed', null=True)),
                ('processed_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount actually processed for refund', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(help_text='Customer requesting the refund', on_delete=django.db.models.deletion.CASCADE, related_name='refund_requests', to=settings.AUTH_USER_MODEL)),
                ('payment', models.ForeignKey(help_text='Payment to be refunded', on_delete=django.db.models.deletion.CASCADE, related_name='refund_requests', to='payments_app.payment')),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Admin who reviewed this request', limit_choices_to={'is_staff': True}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_refund_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Refund Request',
                'verbose_name_plural': 'Refund Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['customer', '-payment_date'], name='payments_ap_custome_1a6605_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['provider', '-payment_date'], name='payments_ap_provide_e181f1_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_status'], name='payments_ap_payment_615071_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['stripe_payment_intent_id'], name='payments_ap_stripe__ca1d3a_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['booking'], name='payments_ap_booking_514588_idx'),
        ),
        migrations.AddConstraint(
            model_name='payment',
            constraint=models.UniqueConstraint(condition=models.Q(('payment_status', 'succeeded')), fields=('booking',), name='unique_booking_successful_payment'),
        ),
        migrations.AddIndex(
            model_name='refundrequest',
            index=models.Index(fields=['customer', '-created_at'], name='payments_ap_custome_821e3f_idx'),
        ),
        migrations.AddIndex(
            model_name='refundrequest',
            index=models.Index(fields=['request_status'], name='payments_ap_request_8c16a7_idx'),
        ),
        migrations.AddIndex(
            model_name='refundrequest',
            index=models.Index(fields=['payment'], name='payments_ap_payment_1384cf_idx'),
        ),
        migrations.AddIndex(
            model_name='refundrequest',
            index=models.Index(fields=['reviewed_by'], name='payments_ap_reviewe_7318e5_idx'),
        ),
    ]
